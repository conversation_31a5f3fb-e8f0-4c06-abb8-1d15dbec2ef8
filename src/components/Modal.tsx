import React from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from './ui/dialog';
import { cn } from '@/lib/utils';

type Props = {
  trigger?: React.ReactNode;
  children: React.ReactNode;
  className?: string;
  title?: React.ReactNode;
  defaultOpen?: boolean;
  onOpenChange?: (open: boolean) => void;
  openModal?: boolean;
  onCloseModal?: (openModal: boolean) => void;
  isOverLayer?: boolean;
  isCloseIcon?: boolean;
  titleAlign?: 'start' | 'end' | 'center';
};

const Modal = ({
  trigger,
  children,
  className = '',
  title = '',
  defaultOpen,
  onOpenChange,
  openModal,
  isOverLayer,
  isCloseIcon = true,
  titleAlign,
  onCloseModal,
}: Props) => {
  return (
    <Dialog onOpenChange={onOpenChange} defaultOpen={defaultOpen} open={openModal}>
      <DialogTrigger asChild>{trigger}</DialogTrigger>
      <DialogContent
        isOverLayer={isOverLayer}
        onCloseModal={onCloseModal}
        className={cn('max-w-[425px]', className)}
        isCloseIcon={isCloseIcon}
      >
          <DialogHeader>
            <DialogTitle
              style={{
                textAlign: titleAlign || 'start',
              }}
              className="text-lg font-semibold leading-9 tracking-[0.6px]"
            >
              {title}
            </DialogTitle>
          </DialogHeader>
        {children}
      </DialogContent>
    </Dialog>
  );
};
export default Modal;
