import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../ui/tooltip';
import { TooltipPortal } from '@radix-ui/react-tooltip';
import React from 'react';
import { cn } from '@/lib/utils';

interface Props {
  children?: React.ReactNode;
  trigger: React.ReactNode;
  className?: string;
  side?: 'top' | 'right' | 'bottom' | 'left';
  sideOffset?: number;
  delayDuration?: number;
}

export const TooltipWrapper = (props: Props) => {
  const { trigger, children, delayDuration, sideOffset, className, side = 'top' } = props;
  return (
    <TooltipProvider delayDuration={delayDuration ?? 0}>
      <Tooltip>
        <TooltipTrigger asChild>
          {trigger}
        </TooltipTrigger>
        <TooltipPortal>
          <TooltipContent side={side} sideOffset={sideOffset} className={cn(className)}>
            {children}
          </TooltipContent>
        </TooltipPortal>
      </Tooltip>
    </TooltipProvider>
  );
};
